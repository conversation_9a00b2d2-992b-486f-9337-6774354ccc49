/* ===== 现代化动画系统 ===== */

/* 性能优化的动画基础类 */
.animate-optimized {
    will-change: transform, opacity;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Fade animations with opacity */
@keyframes fade-in {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

/* 现代化淡入动画 */
@keyframes modern-fade-in {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fade-out {
    0% {
        opacity: 1;
    }

    100% {
        opacity: 0;
    }
}

/* Pop animations with opacity and vertical scaling */
@keyframes pop-in {
    0% {
        opacity: 0;
        transform: scaleY(0);
    }

    /* Make the scaling faster on pop-in, otherwise it looks a bit weird */
    33% {
        transform: scaleY(1);
    }

    100% {
        opacity: 1;
        transform: scaleY(1);
    }
}

/* 现代化弹出动画 */
@keyframes modern-pop-in {
    0% {
        opacity: 0;
        transform: scale(0.8) translateY(20px);
    }
    50% {
        transform: scale(1.02) translateY(-5px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* 平滑滑入动画 */
@keyframes slide-in-right {
    0% {
        opacity: 0;
        transform: translateX(30px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 微妙的悬停动画 */
@keyframes gentle-bounce {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-2px);
    }
}

@keyframes pop-out {
    0% {
        opacity: 1;
        transform: scaleY(1);
    }

    66% {
        transform: scaleY(1);
    }

    100% {
        opacity: 0;
        transform: scaleY(0);
    }
}

/* Flashing for highlighting animation */
@keyframes flash {

    0%,
    50%,
    100% {
        opacity: 1;
    }

    25%,
    75% {
        opacity: 0.2;
    }
}

/* Pulsing highlight, slightly resizing the element */
@keyframes pulse {
    from {
        transform: scale(1);
        filter: brightness(1.1);
    }

    to {
        transform: scale(1.01);
        filter: brightness(1.3);
    }
}

/* Ellipsis animation */
@keyframes ellipsis {
    0% {
        content: ""
    }

    25% {
        content: "."
    }

    50% {
        content: ".."
    }

    75% {
        content: "..."
    }
}

/* HEINOUS */
@keyframes infinite-spinning {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

/* STscript animation */
@keyframes script_progress_pulse {

    0%,
    100% {
        border-top-color: var(--progColor);
    }

    50% {
        border-top-color: var(--progFlashColor);
    }
}

/* Scroll delay animations */
@keyframes hide-scroll {

    from,
    to {
        overflow-y: hidden;
    }
}
