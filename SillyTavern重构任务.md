# Context
Filename: SillyTavern重构任务.md
Created On: 2025-01-08 
Created By: AI Assistant
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
对SillyTavern-release项目进行重构，并根据AI小说IDE详细执行计划.md文件来添加新功能。

具体要求：
1. 首先分析现有的SillyTavern-release项目结构和代码架构
2. 阅读并理解AI小说IDE详细执行计划.md文件中的功能需求和实现方案
3. 制定详细的重构计划，包括：
   - UI界面的重新设计和优化
   - 新功能模块的集成方案
   - 代码结构的改进建议
   - 兼容性和稳定性考虑
4. 按照计划文件中的要求，逐步实现新功能
5. 确保重构后的代码符合现代前端开发规范
6. 提供详细的实现文档和进度报告

# Project Overview
这是一个将传统的SillyTavern AI聊天应用重构为现代化AI小说IDE的项目。目标是保留SillyTavern的核心功能（角色管理、世界书、AI集成），同时添加专业的小说创作功能，并采用Tauri + React + TypeScript的现代技术栈。

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)

## SillyTavern项目分析结果

### 技术架构分析
- **后端**: Node.js + Express服务器架构
- **前端**: 传统Web技术栈（jQuery、原生JavaScript、HTML/CSS）
- **数据存储**: 本地文件系统 + JSON配置文件
- **AI集成**: 支持多种AI模型（OpenAI、Claude、本地模型等）
- **扩展系统**: 基于JavaScript的插件架构

### 核心功能模块
1. **角色管理系统** (char-data.js, personas.js)
   - PNG角色卡解析和导入
   - 角色属性和设定管理
   - 角色切换和多角色支持

2. **世界书系统** (world-info.js)
   - 关键词激活机制（AND_ANY, AND_ALL, NOT_ALL, NOT_ANY逻辑）
   - 递归扫描和条目激活
   - 时间效应管理（Sticky, Cooldown, Delay）
   - Token预算分配算法

3. **AI交互核心** (openai.js, textgen-settings.js, kai-settings.js)
   - 多AI模型适配器
   - 流式响应处理
   - 上下文管理和Token控制
   - 采样参数配置

4. **聊天管理** (chats.js, group-chats.js)
   - 对话历史管理
   - 群组聊天支持
   - 消息格式化和显示

5. **扩展系统** (extensions/)
   - 向量搜索扩展
   - 记忆增强扩展
   - 表达式系统
   - 第三方集成

### 关键技术特点
- **PNG角色卡解析**: 使用png-chunk-text和png-chunks-extract库
- **世界书激活引擎**: 复杂的关键词匹配和逻辑判断系统
- **流式AI响应**: Server-Sent Events实现实时生成显示
- **本地存储**: 基于文件系统的数据持久化
- **模块化设计**: 功能模块相对独立，便于重构

# Implementation Plan (Generated by PLAN mode)

## 重构策略概述
采用渐进式现代化重构方案，分为5个主要阶段：
1. **设计系统建立** - 创建统一的设计规范和CSS变量系统
2. **核心样式重构** - 重构主要CSS文件和布局系统
3. **组件界面优化** - 优化各功能模块的界面设计
4. **响应式完善** - 增强移动端和多屏幕适配
5. **性能优化** - 优化加载性能和用户体验

## 详细变更计划

### 阶段1: 设计系统建立
**目标**: 建立统一的设计规范和现代化的CSS变量系统

**文件变更**:
- `public/css/design-system.css` (新建) - 设计系统核心文件
- `public/style.css` - 更新CSS变量定义
- `public/css/st-tailwind.css` - 增强工具类

**具体修改**:
1. 创建现代化的颜色系统
2. 定义统一的字体和间距规范
3. 建立组件样式基础类
4. 优化CSS变量命名和组织

### 阶段2: 核心样式重构
**目标**: 重构主要界面布局和核心样式

**文件变更**:
- `public/style.css` - 核心样式重构
- `public/index.html` - HTML结构优化
- `public/css/mobile-styles.css` - 移动端样式增强

**具体修改**:
1. 重构主布局系统，使用CSS Grid
2. 优化顶部工具栏设计
3. 改进侧边栏抽屉效果
4. 增强聊天界面视觉设计

### 阶段3: 组件界面优化
**目标**: 优化各功能模块的界面设计和用户体验

**文件变更**:
- `public/css/popup.css` - 弹窗组件优化
- `public/css/promptmanager.css` - 提示词管理器界面
- `public/css/world-info.css` - 世界信息界面
- `public/css/extensions-panel.css` - 扩展面板界面
- `public/css/tags.css` - 标签系统界面

**具体修改**:
1. 统一按钮和表单元素样式
2. 优化卡片和列表组件设计
3. 增强交互反馈效果
4. 改进图标和文字排版

### 阶段4: 响应式完善
**目标**: 完善响应式设计，优化多设备体验

**文件变更**:
- `public/css/mobile-styles.css` - 移动端样式完善
- `public/style.css` - 响应式断点优化
- `public/index.html` - 移动端适配优化

**具体修改**:
1. 优化移动端导航和布局
2. 改进触摸交互体验
3. 适配不同屏幕尺寸
4. 优化移动端性能

### 阶段5: 性能优化
**目标**: 优化加载性能和整体用户体验

**文件变更**:
- `public/css/animations.css` - 动画效果优化
- `public/css/loader.css` - 加载状态优化
- `public/style.css` - CSS性能优化

**具体修改**:
1. 优化CSS加载顺序
2. 减少重绘和回流
3. 增强加载状态反馈
4. 添加平滑过渡动画

## 风险评估与解决方案

### 主要风险点
1. **功能兼容性风险**
   - 风险: CSS重构可能影响现有JavaScript功能
   - 解决方案: 保持现有CSS类名，仅修改样式属性
   - 测试策略: 每个阶段完成后进行全功能测试

2. **性能影响风险**
   - 风险: 新增CSS可能影响页面加载性能
   - 解决方案: 使用CSS压缩和优化工具
   - 监控策略: 使用Lighthouse进行性能监控

3. **浏览器兼容性风险**
   - 风险: 现代CSS特性可能在旧浏览器中不支持
   - 解决方案: 提供CSS回退方案和polyfill
   - 测试范围: 支持Chrome 90+, Firefox 88+, Safari 14+

### 回滚策略
- 每个阶段开始前创建代码备份
- 使用Git分支管理，便于快速回滚
- 保留原始CSS文件作为备份

## 测试策略

### 功能测试
1. **核心功能验证**
   - 聊天消息发送和接收
   - 角色创建和管理
   - 扩展系统加载
   - 设置保存和加载

2. **界面交互测试**
   - 所有按钮和链接点击
   - 表单输入和提交
   - 拖拽功能验证
   - 键盘导航测试

3. **响应式测试**
   - 不同屏幕尺寸适配
   - 移动端触摸交互
   - 横竖屏切换
   - 缩放功能测试

### 性能测试
- 页面加载时间测试
- CSS渲染性能测试
- 内存使用监控
- 动画流畅度测试
- **UI组件库**: Mantine 7.x（支持复杂应用和模块化界面）
- **编辑器**: Monaco Editor（VS Code同款）
- **状态管理**: Zustand + Immer + TanStack Query
- **可视化**: Cytoscape.js（知识图谱）+ React Flow（工作流）

### 核心功能需求
1. **模块化界面系统**
   - 5个主要工作区：编辑器、角色管理、知识图谱、项目管理、设置配置
   - 工作区切换机制和状态保持
   - 专注性原则，避免功能过载

2. **全局酒馆控制系统**
   - 动态指令工程核心（基于st-memory-enhancement）
   - AI轮询和密钥管理
   - SillyTavern兼容的角色管理
   - 世界书激活引擎

3. **智能知识库系统**
   - 本地知识库管理（SillyTavern世界书兼容）
   - 结构化记忆存储
   - RAG增强生成

4. **知识图谱可视化系统**
   - 基于Graphiti的知识图谱集成
   - 人物关系网络构建
   - 交互式图谱浏览器

5. **多章节改编引擎**
   - 笔墨星河算法集成
   - 雷点识别系统
   - 批量处理引擎

### 集成方案需求
- **Graphiti知识图谱**: MCP协议集成，实时同步，查询优化
- **笔墨星河功能**: 算法移植，文本分析，人物关系提取
- **SillyTavern生态**: 完全兼容角色卡、世界书、预设系统

## 技术约束和挑战
1. **架构迁移复杂性**: 从Node.js服务器架构迁移到Tauri桌面应用
2. **功能兼容性**: 保持SillyTavern生态的完全兼容
3. **性能优化**: 大文件处理、实时图谱渲染、AI响应流式显示
4. **数据迁移**: 现有用户数据的平滑迁移方案
5. **扩展系统**: 重新设计插件架构以适应新技术栈
