# Context
Filename: SillyTavern重构任务.md
Created On: 2025-01-08 
Created By: AI Assistant
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
对SillyTavern-release项目进行重构，并根据AI小说IDE详细执行计划.md文件来添加新功能。

具体要求：
1. 首先分析现有的SillyTavern-release项目结构和代码架构
2. 阅读并理解AI小说IDE详细执行计划.md文件中的功能需求和实现方案
3. 制定详细的重构计划，包括：
   - UI界面的重新设计和优化
   - 新功能模块的集成方案
   - 代码结构的改进建议
   - 兼容性和稳定性考虑
4. 按照计划文件中的要求，逐步实现新功能
5. 确保重构后的代码符合现代前端开发规范
6. 提供详细的实现文档和进度报告

# Project Overview
这是一个将传统的SillyTavern AI聊天应用重构为现代化AI小说IDE的项目。目标是保留SillyTavern的核心功能（角色管理、世界书、AI集成），同时添加专业的小说创作功能，并采用Tauri + React + TypeScript的现代技术栈。

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)

## SillyTavern项目分析结果

### 技术架构分析
- **后端**: Node.js + Express服务器架构
- **前端**: 传统Web技术栈（jQuery、原生JavaScript、HTML/CSS）
- **数据存储**: 本地文件系统 + JSON配置文件
- **AI集成**: 支持多种AI模型（OpenAI、Claude、本地模型等）
- **扩展系统**: 基于JavaScript的插件架构

### 核心功能模块
1. **角色管理系统** (char-data.js, personas.js)
   - PNG角色卡解析和导入
   - 角色属性和设定管理
   - 角色切换和多角色支持

2. **世界书系统** (world-info.js)
   - 关键词激活机制（AND_ANY, AND_ALL, NOT_ALL, NOT_ANY逻辑）
   - 递归扫描和条目激活
   - 时间效应管理（Sticky, Cooldown, Delay）
   - Token预算分配算法

3. **AI交互核心** (openai.js, textgen-settings.js, kai-settings.js)
   - 多AI模型适配器
   - 流式响应处理
   - 上下文管理和Token控制
   - 采样参数配置

4. **聊天管理** (chats.js, group-chats.js)
   - 对话历史管理
   - 群组聊天支持
   - 消息格式化和显示

5. **扩展系统** (extensions/)
   - 向量搜索扩展
   - 记忆增强扩展
   - 表达式系统
   - 第三方集成

### 关键技术特点
- **PNG角色卡解析**: 使用png-chunk-text和png-chunks-extract库
- **世界书激活引擎**: 复杂的关键词匹配和逻辑判断系统
- **流式AI响应**: Server-Sent Events实现实时生成显示
- **本地存储**: 基于文件系统的数据持久化
- **模块化设计**: 功能模块相对独立，便于重构

## AI小说IDE执行计划分析结果

### 目标技术栈
- **桌面应用框架**: Tauri 2.0（高性能、安全、小体积）
- **前端框架**: React 18 + TypeScript + Vite
- **UI组件库**: Mantine 7.x（支持复杂应用和模块化界面）
- **编辑器**: Monaco Editor（VS Code同款）
- **状态管理**: Zustand + Immer + TanStack Query
- **可视化**: Cytoscape.js（知识图谱）+ React Flow（工作流）

### 核心功能需求
1. **模块化界面系统**
   - 5个主要工作区：编辑器、角色管理、知识图谱、项目管理、设置配置
   - 工作区切换机制和状态保持
   - 专注性原则，避免功能过载

2. **全局酒馆控制系统**
   - 动态指令工程核心（基于st-memory-enhancement）
   - AI轮询和密钥管理
   - SillyTavern兼容的角色管理
   - 世界书激活引擎

3. **智能知识库系统**
   - 本地知识库管理（SillyTavern世界书兼容）
   - 结构化记忆存储
   - RAG增强生成

4. **知识图谱可视化系统**
   - 基于Graphiti的知识图谱集成
   - 人物关系网络构建
   - 交互式图谱浏览器

5. **多章节改编引擎**
   - 笔墨星河算法集成
   - 雷点识别系统
   - 批量处理引擎

### 集成方案需求
- **Graphiti知识图谱**: MCP协议集成，实时同步，查询优化
- **笔墨星河功能**: 算法移植，文本分析，人物关系提取
- **SillyTavern生态**: 完全兼容角色卡、世界书、预设系统

## 技术约束和挑战
1. **架构迁移复杂性**: 从Node.js服务器架构迁移到Tauri桌面应用
2. **功能兼容性**: 保持SillyTavern生态的完全兼容
3. **性能优化**: 大文件处理、实时图谱渲染、AI响应流式显示
4. **数据迁移**: 现有用户数据的平滑迁移方案
5. **扩展系统**: 重新设计插件架构以适应新技术栈
