/**
 * Silly<PERSON><PERSON>n 设计系统核心文件
 * 
 * 提供统一的设计规范和现代化的CSS变量系统
 * 专注于桌面端界面优化
 */

:root {
    /* ===== 现代化颜色系统 ===== */
    
    /* 主色调 - 蓝色系 */
    --primary-50: #f0f9ff;
    --primary-100: #e0f2fe;
    --primary-200: #bae6fd;
    --primary-300: #7dd3fc;
    --primary-400: #38bdf8;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;
    
    /* 表面颜色 - 灰色系 */
    --surface-50: #f8fafc;
    --surface-100: #f1f5f9;
    --surface-200: #e2e8f0;
    --surface-300: #cbd5e1;
    --surface-400: #94a3b8;
    --surface-500: #64748b;
    --surface-600: #475569;
    --surface-700: #334155;
    --surface-800: #1e293b;
    --surface-900: #0f172a;
    
    /* 语义化颜色 */
    --success-50: #f0fdf4;
    --success-500: #22c55e;
    --success-600: #16a34a;
    --success-700: #15803d;
    
    --warning-50: #fffbeb;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --warning-700: #b45309;
    
    --error-50: #fef2f2;
    --error-500: #ef4444;
    --error-600: #dc2626;
    --error-700: #b91c1c;
    
    /* 中性颜色增强 */
    --neutral-50: #fafafa;
    --neutral-100: #f5f5f5;
    --neutral-200: #e5e5e5;
    --neutral-300: #d4d4d4;
    --neutral-400: #a3a3a3;
    --neutral-500: #737373;
    --neutral-600: #525252;
    --neutral-700: #404040;
    --neutral-800: #262626;
    --neutral-900: #171717;
    
    /* ===== 统一间距系统 ===== */
    --spacing-0: 0;
    --spacing-px: 1px;
    --spacing-0_5: 0.125rem;  /* 2px */
    --spacing-1: 0.25rem;     /* 4px */
    --spacing-1_5: 0.375rem;  /* 6px */
    --spacing-2: 0.5rem;      /* 8px */
    --spacing-2_5: 0.625rem;  /* 10px */
    --spacing-3: 0.75rem;     /* 12px */
    --spacing-3_5: 0.875rem;  /* 14px */
    --spacing-4: 1rem;        /* 16px */
    --spacing-5: 1.25rem;     /* 20px */
    --spacing-6: 1.5rem;      /* 24px */
    --spacing-7: 1.75rem;     /* 28px */
    --spacing-8: 2rem;        /* 32px */
    --spacing-9: 2.25rem;     /* 36px */
    --spacing-10: 2.5rem;     /* 40px */
    --spacing-11: 2.75rem;    /* 44px */
    --spacing-12: 3rem;       /* 48px */
    --spacing-14: 3.5rem;     /* 56px */
    --spacing-16: 4rem;       /* 64px */
    --spacing-20: 5rem;       /* 80px */
    --spacing-24: 6rem;       /* 96px */
    --spacing-28: 7rem;       /* 112px */
    --spacing-32: 8rem;       /* 128px */
    
    /* ===== 字体系统 ===== */
    --font-family-sans: 'Noto Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-mono: 'Noto Sans Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    
    /* 字体大小 */
    --text-xs: 0.75rem;       /* 12px */
    --text-sm: 0.875rem;      /* 14px */
    --text-base: 1rem;        /* 16px */
    --text-lg: 1.125rem;      /* 18px */
    --text-xl: 1.25rem;       /* 20px */
    --text-2xl: 1.5rem;       /* 24px */
    --text-3xl: 1.875rem;     /* 30px */
    --text-4xl: 2.25rem;      /* 36px */
    --text-5xl: 3rem;         /* 48px */
    
    /* 行高 */
    --leading-none: 1;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;
    
    /* 字重 */
    --font-thin: 100;
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --font-extrabold: 800;
    --font-black: 900;
    
    /* ===== 圆角系统 ===== */
    --radius-none: 0;
    --radius-sm: 0.125rem;    /* 2px */
    --radius-base: 0.25rem;   /* 4px */
    --radius-md: 0.375rem;    /* 6px */
    --radius-lg: 0.5rem;      /* 8px */
    --radius-xl: 0.75rem;     /* 12px */
    --radius-2xl: 1rem;       /* 16px */
    --radius-3xl: 1.5rem;     /* 24px */
    --radius-full: 9999px;
    
    /* ===== 阴影系统 ===== */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    
    /* ===== 动画系统 ===== */
    --duration-75: 75ms;
    --duration-100: 100ms;
    --duration-150: 150ms;
    --duration-200: 200ms;
    --duration-300: 300ms;
    --duration-500: 500ms;
    --duration-700: 700ms;
    --duration-1000: 1000ms;
    
    /* 缓动函数 */
    --ease-linear: linear;
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    
    /* ===== Z-index 层级系统 ===== */
    --z-0: 0;
    --z-10: 10;
    --z-20: 20;
    --z-30: 30;
    --z-40: 40;
    --z-50: 50;
    --z-auto: auto;
    
    /* 特殊层级 */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
    
    /* ===== 断点系统（桌面端专用）===== */
    --breakpoint-sm: 640px;   /* 小屏桌面 */
    --breakpoint-md: 768px;   /* 中等桌面 */
    --breakpoint-lg: 1024px;  /* 大屏桌面 */
    --breakpoint-xl: 1280px;  /* 超大桌面 */
    --breakpoint-2xl: 1536px; /* 4K显示器 */
    
    /* ===== 组件特定变量 ===== */
    
    /* 按钮 */
    --button-height-sm: 2rem;     /* 32px */
    --button-height-md: 2.5rem;   /* 40px */
    --button-height-lg: 3rem;     /* 48px */
    --button-padding-x-sm: var(--spacing-3);
    --button-padding-x-md: var(--spacing-4);
    --button-padding-x-lg: var(--spacing-6);
    
    /* 输入框 */
    --input-height-sm: 2rem;      /* 32px */
    --input-height-md: 2.5rem;    /* 40px */
    --input-height-lg: 3rem;      /* 48px */
    --input-padding-x: var(--spacing-3);
    
    /* 卡片 */
    --card-padding: var(--spacing-6);
    --card-radius: var(--radius-lg);
    --card-shadow: var(--shadow-base);
    
    /* 导航栏 */
    --navbar-height: 4rem;        /* 64px */
    --navbar-padding-x: var(--spacing-6);
    
    /* 侧边栏 */
    --sidebar-width: 16rem;       /* 256px */
    --sidebar-width-collapsed: 4rem; /* 64px */
}

/* ===== 基础组件样式类 ===== */

/* 按钮基础类 */
.btn-base {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    font-weight: var(--font-medium);
    transition: all var(--duration-150) var(--ease-in-out);
    cursor: pointer;
    border: 1px solid transparent;
    text-decoration: none;
    white-space: nowrap;
}

.btn-sm {
    height: var(--button-height-sm);
    padding: 0 var(--button-padding-x-sm);
    font-size: var(--text-sm);
}

.btn-md {
    height: var(--button-height-md);
    padding: 0 var(--button-padding-x-md);
    font-size: var(--text-base);
}

.btn-lg {
    height: var(--button-height-lg);
    padding: 0 var(--button-padding-x-lg);
    font-size: var(--text-lg);
}

/* 卡片基础类 */
.card-base {
    background: var(--surface-50);
    border: 1px solid var(--surface-200);
    border-radius: var(--card-radius);
    box-shadow: var(--card-shadow);
    padding: var(--card-padding);
}

/* 输入框基础类 */
.input-base {
    width: 100%;
    border: 1px solid var(--surface-300);
    border-radius: var(--radius-md);
    padding: 0 var(--input-padding-x);
    font-size: var(--text-base);
    transition: border-color var(--duration-150) var(--ease-in-out);
    background: var(--surface-50);
}

.input-base:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
